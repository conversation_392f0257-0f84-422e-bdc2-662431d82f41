
--- Page 1 ---
Lec	1	Wireless	network	and	their	importance	• WiFi:	For	LAN	-	High	speed	internet	access	• Cellular:	4G,	5G	-	Incredible	communication	over	wide	area	• Bluetooth:	Short	range	connectivity	for	device	like	headphone	• Zigbee:	Low-power,	low	data	rate	-	For	home	IoT,	smart	home	• Satellite:	Internet	access	in	remote	areas,	maritime	area,	and	aviation	• RFID	(Radio	Freq.	Id.):	Used	for	tracking	and	inventory	management	• LoRaWAN	(Long	Range	Wide	Area):	Low	power	long	range	for	IoT	apps,	cities	• IoT	and	wireless	network:	IoT	rely	on	wireless	network.	Need	strong	connectivity	Intro	(IoT)	• Genesis:	IoT	began	2008-2009	when	number	of	connected	device	surpassed	humans	• IoT	apps:	Smart	home,	smart	city,	connected	car,	healthcare,	smart	farming	• E.g.,	Google	car,	smart	traffic	control,	utilities	• Challenges:	Connectivity,	Big	data,	Security,	Awareness,	Business	model	What	are	IoT	"things"?	• Sensors:	Activate	smart	objects	• Sensors:	Detect	physical/biological/chemical	changes	(temperature,	pressure)	• Transducers:	Convert	energy	from	one	to	another	• Types	of	sensors:	• Active:	Emit	signal	and	measure	response	(ultrasonic	for	distance)	• Passive:	No	emit	signal,	but	detect	environment	(light	sensors,	LDR)	• Non-invasive:	Measure	without	penetration	(optical	glucose)	• Invasive:	Require	contact	with	subject	(needle-based	glucose)	• Contact:	Require	physical	touch	• Non-contact:	Measure	from	distance	(infrared)	Sensors	• Position	sensors:	Measure	position	(absolute	or	relative,	linear,	angular)	• Example:	Potentiometer,	inclinometer,	army/military	sensor	• Occupancy	sensors:	Detect	presence	even	if	no	motion	• Motion	detector,	movement	• Example:	Electric	eye,	radar,	sonar,	wave	• Velocity	and	Acceleration	Sensors:	Measure	speed	(linear/angular)	and	changes	in	velocity	• Example:	Accelerometer,	gyroscope	• Chemical:	chemical	concentrations	• Example:	Breathalyzer,	alcohol	meter,	smoke	detector	
--- Page 2 ---
• Biosensor:	Detect	biological	elements	(organism,	tissues,	enzyme)	• Examples:	Blood	glucose	biosensor,	pulse	oximeter,	electrocardiogram	• Humidity	Sensors:	Water	vapor	in	air	or	mass	(absolute,	relative)	• Example:	Hygrometer,	humistor,	soil	moisture	sensors	Sensors	in	Smartphones	• Proximity,	RGB	light,	Hall,	Barometer	sensors	• Sensors	Industry:	Trillion-sensor	economy	Automatic	Traffic	Monitoring	System	1. Main	Road	(MR)	Green,	state	2	all	time,	Side	Road	(SR)	Red	always	2. If	a	car	is	sensed	on	SR,	wait	1	minutes	from	MR	→	Yellow	for	10s	• MR	→	Red,	SR	→	Green	120s	• MR	→	Red,	SR	→	Yellow	10s	• Return	to	step	1	Lec	2		Sensors	+	Actuators	and	MEMS	• Actuators:	Perform	actions	based	on	sensory	data	(moving	a	lever)	• MEMS	(Micro-Electro-Mechanical	System):	Miniaturized	system	combining	actuators	+	sensors	• Classification:	• Motion:	Linear,	rotary,	multi-axis	• Power:	High,	low	• Output:	Binary,	continuous	• apps:	Industry-specific	• Energy	type:	Mechanical,	electrical,	micro	• MEMS:	Micro-scale	systems	(<1	mm)	integration	electronics	and	mechanical	components	(microelectronic	circuits,	microstructure	elements)	• Example:	Sensors	and	actuators	in	tiny	devices	Smart	Objects	• Definition:	Build	blocks	of	IoT,	turning	everyday	objects	into	networked	intelligence	system.	"Power	lies	in	networking,	not	isolation"	Characteristics	• Sensor	• Actuators	• Tiny	computer	+	embed	processing	for	smart	behaviors	• Communication	capabilities:	Send/receive	data	• Power	source:	Solar,	battery	• E.g.,	Arduino	
--- Page 3 ---
Main	Trends	• Decreasing	size:	MEMS	enable	tiny	invisible	smart	objects	• Decreasing	power	consumption:	Passive	sensors,	long-lasting	batteries,	10	years	• Increasing	power	of	processing:	Smaller,	more	powerful	processors	• Improving	communication:	Faster,	wireless	speed,	longer	range,	specialized	IoT	protocols	Electronic	Communication	Systems	• System	components:	Transmitter	→	Channel	→	Receiver	→	output	signal	• Radio	communication	example		• sph	→	Transducer	→	Electronic	signal	→	Electronic	processor	→	Transmitter	→	Receiver	→Transducer	→	signal	Transmitter	:	Generates	radio	frequency	alternating	current:	Antenna																Emit	radio	wave	→	Generate	Antenna	:	Converts	electrical	signals	to	radio	waves	(transmitter)	• Types:	Basic	dipole,	Floded	dipole,	dipole	with	reflector,	Yagi	• Feature:	Directionality,	gain,	bandwidth,	frequency	• Design:	Resonance	principle,	antenna,	electrons	reflect	based	on	dielectric	changes	• Operation:	Half-wave	dipole	creates	electric	field	loops	(radio	wave)	at	light	speed	to	receiver	Receiver	:	Intercept	radio	wave	via	antenna,	filters	desired	signals,	amplifies,	and	demodulates	to	extract	information	Transceiver	:	Combines	transmitter	and	receiver	(filter)	in	one	device	(modem	with	modulation/demodulation)	Communication	Channel	:	Physical	(wired)	or	logical	(radio),	capacity	measured	in	bandwidth	(Hz)	or	data	rate	(bits/s)	Transmission	Media	• Guided	(wired):	Twisted,	coaxial,	fiber	optic,	cables	• Unguided	(wireless):	Free	space		Lec	3						• Unwanted	Effects	in	Communication	Systems	• Attenuation,	distortion	,	Interference	• Attenuation	:	Reduction	in	signal	strength	at	the	receiver	• 	• Av=Vout/VinAv=VinVout	(Source/dest.)	Distortion	

--- Page 4 ---
• Waveform	perturbation	due	to	imperfect	system	response	• Disappears	when	signal	off,	can	be	corrected	with	equalizer	if	channel	response	is	linear	Interference	• Contamination	by	extraneous	signal	(other	transmitters,	power	lines)	• Example:	Cellular	network	interference	between	base	stations	Noise	• Random	signal	alteration,	causing	altered	signal	spectrum	• Harder	to	mitigate	than	distortion	Signal	Gain	and	Attenuation	Measurement	• Gain	=	Amplification	Definitions	:	Amplification;	ratio	of	output	to	input	• Voltage	gain:	Av=Vout/VinAv=Vin/Vout	• Power	gain:	Ap=Pout/PinAp=Pin///Pout	• Cascaded	system:	total	gain	=	product	of	individual	gains	• Eg : 3×5×4=60																	Attenuation	• Signal	loss	ratio	of	output	to	input	<1	,	buufer	=1	• Attenuation:	A=Vout/VinA=Vin//Vout	Cascaded	Attenuation:	product	of	individual	attenuations	Mixed	system	Total	gain	=	product	of	gains	and	attenuations																Decibels	• Express	gain/loss	logarithmically	• Voltage	dB:	20log(Vout/Vin)	• Power	dB:	10log(Pout/Pin)	• dB	+	→gain	|	dB	-	→		attenuation	|	db	0	→		buffer	• Cascaded	dB:	Algebraic	sum	(15	-	20	+	35)	Anti-log			Lec	4		• Frequency	Spectrum	• Long-range	Licensed	Spectrum	:Reserved	by	FCC	for	organization	(cellular,	radio/tv)	• Short-range	Unlicensed	Spectrum	• Open	for	public	use	(WiFi)	• 6	GHz	expanded	in	2020	• Communication	Criteria	

--- Page 5 ---
• Range	:	Signal	propagation	distance	[short,	medium,	long]	• Frequency	band	:	Licensed/Unlicensed	• Power	consumption	:Stable	power	vs.	battery-powered	devices	• Topology	:	Network	layouts	for	smart	objects	• Constrained	Devices/Network	:	Limitation	and	challenges	in	connectivity	
	• IoT	Access	Technologies	• Short	Range	:	Tens	of	meters	(IEEE	802.15.4,	Bluetooth)	• Medium	Range	:	Tens	of	hundreds	meters,	1-5	miles	(IEEE	802.11	WiFi,	IEEE	802.15)	• Long	Range	• 7	miles	(cellular,	outdoor	WiFi	,	LPWA,	cellular,	low	power	wide	area)																				Protocols		• ZigBee,	Thread,	SNAP:	IEEE	802.15.4	• Bluetooth	:	IEEE	802.15.1,	visible	light	communication	• WiFi	:IEEE	802.11	• Power	line	connection	:	1901.2a	• Smart	cites	:	802.15,4	gle	• LoRaWAN	:	Low	power,	low	range	unlicensed	• OMB-IoT,	LTE-based	licensed/long		Lec	5		• IEEE	802.15.4	Standard	• Physical	Layer	and	Media	(MAC)	• Form	basis	of	ZigBee,	Thread,	SNAP	• Define	operation	of	low-rate	wireless	personal	area	network	(LR-WAN)	• ZigBee	Technology	• Designed	as	cost-effective,	low	power	solution	• Used	in	home	automation,	smart	energy,	sensors	networks	• Operates	in	[2.4	GHz	ISM	band,	16	channels]			Zigbee	alliance	:	Over	400	companies	collaborate	to	define	and	use	Zigbee	shields	with	a	variety	of	apps.	[	Smart	energy	and	building	automation	]	General	Characteristics	of	Zigbee	• Low	power:	Devices	run	for	years	on	AA	batteries,	up	to	20	years	with	special	designs	(gas	meters).	

--- Page 6 ---
• Low	data	rate:	• 2.4	GHz	band	support	250	Kbps	lower	effective	throughput	due	to	half	duplex.	• Use	16	channels,	5	MHz	spacing.	• Network:	Network	range	from	few	devices	to	thousands,	optimized	by	routing	mechanism.	• 														Zigbee	Mesh	Topology	• Coordinator	(ZC):	Form	network,	store	info,	manage	security	keys.	• Router	(ZR):	Route	packet,	support	mesh	network.	• End	device	(ZE):	Sleep	to	save	power,	relies	on	parent	for	communication.	• Coordinator	can	do:	Start	network,	select	PAN	ID,	operating	channel.	Channel	Selection	• Use	Energy	Detect	scan:Find	quietest	channels.	• Use	Active	scan:	Detect	existing	network	and	avoid	interference.	Zigbee	Range	• Range:	5-9	meters	indoors,	extend	in	open	air.	• Obstruction:	Physical	barrier	reduces	signal.	Network	Synchronization	• End	device	rely	on	awake	parent	to	relay	msg.	• Dedicated	wake	periodically	to	check	for	data.	Sleep	Options	• Pin	sleep:Controlled	by	hardware	pin.	• Cyclic	sleep:Wake	up	periodically	to	check	for	data.	• End	device	send	pull	request	every	10ms	while	awake.	Parent	Operations	• Manage	RF	data,	store	data	for	limited	time.	• Can	only	store	one	broadcast	packet	per	child.					Lec	5	continue...		• Zigbee	Protocol	Stack	• Physical	Layer	• Operate	in	868/915	MHz	and	2.4	GHz	frequency	bands	• Responsible	for	packet	generation	and	reception	• Data	transparency,	power	management	• Network/Transport	Layer	• Allow	send	data	transmission	without	user	awareness	• User	access	remote	resources	if	they	were	local	(cloud	storage)	

--- Page 7 ---
• Medium	Access	Control	(MAC)	• Control	radio	access	via	CSMA-CA	• Beacon	transmission,	synchronization,	and	reliable	radio	link	establishment	• MAC	Frame	Type	[Data	Frame,	Beacon	Frame,	ACK]	• CSMA/CA	Mechanism	• Carrier	sense	multiple	access	with	collision	avoidance	(CA)	• Deal	with	collision	after	occurrence	(	CD	)		• Zigbee	Security	• Provide	[secure	key	establishment,	secure	key	transport]	• Frame		protection	using	symmetric	cryptography,	secure	device	management	• Zigbee	Security	Models	:	Centralized	Security	Model	• Use	trust	center	(network	coordinator)	• For	configuring	and	authenticating	devices	• Generating	and	updating	network	keys	• Securely	manage	network-wide	communication	• Distributed	Security	Model	[	All	nodes	share	the	same	key	-	Router	issues	network	-	key	for	encryption	]	<<Simpler	but	more	secure>>	• Zigbee	WBAN	(Wireless	Body	Area	Network)	human	body	network	over		• Data	confidentiality	:Prevent	unauthorized	access	to	data	,	Use	symmetric	key	encryption	• Data	integrity	:	Prevent	data	alteration	,	Use	MIC	(Message	Integrity	Code)	to	validate	integrity	• Data	Authentication	:	Ensures	authenticity	of	message	source	using	message	authentication	code	(MAC)	• Data	Freshness	• Prevent	replay	attacks	(old	messages	reused)	• Weak	freshness:	Ensuring	ordering	• Strong	freshness:	Ensure	ordering	and	delay	constraining	• Secure	localization	[	Ensure	accurate	location	tracking	-	Prevent	attacker	from	spoofing	location	signals	• Security	Threats	&	Attacks	• Types	of	Attacks	• Authentication	attack	Security	• Service	integrity	attacks	• Denial	of	service	(DoS)	• Physical	Layer	Attack	• Jamming	:Interference	with	radio	signals	• Tampering	Physical	attack	on	nodes		
--- Page 8 ---
Lec	6		• Bluetooth:	:	Special	Interest	Group	SIG	have	over	8K	members	(Cypress,	Qualcomm,	Toshiba)	→	driving	innovations	like	Bluetooth	mesh.	• Bluetooth	operates	at	2.4	GHz	in	unlicensed	ISM	band	shared	with	ZigBee	and	WiFi	protocols,	distinguished	by	standardized	rules.	• Bluetooth	uses	Frequency	hopping	(1600	switches/sec	across	79	1-MHz)	to	prevent	interference	with	devices	like	baby/microwaves.	• Bluetooth	versions:	2004:	2	|	2009:	3		|	2013:	4.1	|	2007:	2.1	|	2010:	4.0	|	2014:	4.2	• 2016	5		(4x	range,	2x	speed,	2	Mbps,	IoT	focus)	• Network	topology:	Bluetooth	network	"piconets"	use	master-and-slave	model.	One	master	connects	[7]	slaves.	Slaves	connect	on	master.	• Master	coordinates	piconets	communication,	sending/receiving	data.	Slaves	cannot	communicate	each	other.	• Devices	can	join	multiple	piconets	(one	as	master),	forming	a	scatternet.	• Bluetooth	addresses	and	name:	• Bluetooth	device	has	a	unique	(48-bit)	address	(BD_ADDR).	11:22:33:44:55:66	• BD_ADDR	=	24-bit	organization	unit	(OUI)	+	24	unique	part	[NAP	(16	bit),	UAP	(8	bits),	LAP	(24	bits)]	• Device	have	user-friendly	names	to	aid	identification	(248	bytes)	• Connection	process:	• Bluetooth	devices	lacking	mutual	information	require	an	inquiry/zone	device	from	(inquiry)	other	devices	respond	with	their	addresses.	• Paging:	forming	connection	between	2	devices	require	prior	inquiry.	• Connection:	states	follow	paging;	device	be	active	or	in	low-power	modes.	• Active:	Regular	data	receive/send	• Sniff:	power-saving,	listen	at	intervals	(10ms)	• Hold:	temporary	sleep,	return	to	active	after	polled	by	master	• Park:	DeepSleep;	slave	inactive	until	master	wakes	it	• Bonding	and	pairing:	• Bonded	device	auto-connect	when	in	range	(phone	to	car	Bluetooth).	Key	• Pairing	creates	bonds;	devices	share	addresses,	names,	profiles,	secret	• Pairing	requires	authentication	(pin	code)	Some	modules	vary	power	• Power	classes:	
--- Page 9 ---
• 	• Bluetooth	protocol	stack:	
• 	• SDP:	Service	Discovery	Protocols,	track	service	in	Dynamic	Environment	via	BDU	• LMP:	Link	Manager,	control	inquiry,	paging,	pairing,	manage	connection	• L2CAP:	Logical	Link	Control	and	Adaptation	Protocol,	provide	data	service	using	ACL	packets	• RFCOMM:	Radio	Frequency	communication,	emulate	serial	ports,	up	to	60	connection	• Bluetooth	profiles:	"Profiles	built	on	Bluetooth	standard	to	define	data	usage	(GAP,	SPP,	FTP)"	• Profiles	determine	application	(HSP	→	headset,	HID	for	controller)	• (FTP)	→	Client	&	browser	files,	server	&	host	file	• (FAX)	→	Gateway	(phone)	provides	fax;	data	terminal	(PC)	send	fax	• (HSP)	→	Gateway	(phone)	manage	audio;	headset	hand	input/output	Lec	7		 Brief History of Bluetooth  • First Formal Version (1999): Introduced Basic Rate (BR) for basic functionalities. • Enhanced Data Rate (EDR): Improved transmission speed by 3x, based on BR. • High Speed (HS, 2009): Utilized Wi-Fi channels for faster data transfer, based on BR/EDR. • Low Energy (LE): Introduced for energy efficiency, optimized for IoT. • Mesh Topology (2016–2017): Enabled devices to form interconnected networks, enhancing IoT applications  

--- Page 10 ---
Bluetooth BED vs. Bluetooth Low Energy (BLE)  Characteristic Bluetooth BED (Prior to 4.1) Bluetooth BED (4.1 onwards) Bluetooth LE (Prior to 4.2) Bluetooth LE (4.2 onwards) RF Physical Channels 79 channels with 1 MHz spacing Same as prior to 4.1 40 channels with 2 MHz spacing Same as prior to 4.2 Discovery/Connect Inquiry/Paging Same as prior to 4.1 Advertising Same as prior to 4.2 Number of Piconet Slaves 7 (active)/255 (total) Same as prior to 4.1 Unlimited Same as prior to 4.2 Device Address Privacy None None Private device addressing Same as prior to 4.2 Max Data Rate 1–3 Mbps Same as prior to 4.1 1 Mbps via GFSK modulation Same as prior to 4.2 Pairing Algorithm Prior to 2.1: E21/E22/SAFER+  2.1–4.0: P-192 Elliptic Curve, HMAC-SHA-256 P-256 Elliptic Curve, HMAC-SHA-256 AES-128 P-256 Elliptic Curve, AES-CMAC Device Authentication E1/SAFER HMAC-SHA-256 AES-CCM Same as prior to 4.2 Encryption Algorithm E0/SAFER+ AES-CCM AES-CCM Same as prior to 4.2 Typical Range 30 m Same as prior to 4.1 50 m Same as prior to 4.2 Max Output Power 100 mW (20 dBm) Same as prior to 4.1 10 mW (10 dBm) Same as prior to 4.2 BLE RF Channels  • RF Channels: BLE uses 40 RF channels with 2 MHz spacing, ranging from 2402 MHz to 2480 MHz. • Primary Advertising Channels: Channels 37, 38, and 39 are used for device discovery. • Secondary Advertisement/Data Channels: The remaining 37 channels are used for secondary advertisements and data transfer during connections. Link Layer States  • Link Layer States: o Standby: Default state; no transmission or reception. o Advertising: Device sends advertising packets for discovery. o Scanning: Device listens for advertising packets. o Initiating: Scanning device attempts to connect to an advertising device. o Connected: Devices are linked, exchanging data regularly. The initiator is the master, and the advertiser is the slave. Observers, Broadcasters, Centrals, and Peripherals o Broadcaster: Transmits only, no receiver needed, no bi-directional data, reduced hardware/software. o Peripheral: Supports bi-directional data, requires full BLE stack and both receiver/transmitter. o Observer: Receives only, no transmitter, no bi-directional data, reduced hardware/software. 
--- Page 11 ---
o Central: Supports bi-directional data, requires full BLE stack and both receiver/transmitter. Generic Access Profile (GAP)  • GAP: Defines how BLE devices interact, covering: o Modes & Roles: Broadcaster, Observer, Peripheral, Central. o Advertisements: Advertising, scanning, parameters, and data. o Connection Establishment: Initiating and accepting connections, connection parameters. o Security: Pairing, bonding, and authentication processes. Advertising and Scanning in BLE  • Advertising Event Device transmits the same packet on Primary Advertising Channels (37, 38, 39). • Passive Scanning Scanner listens for advertising packets without responding. • Active Scanning : Scanner sends SCAN_REQ to request more data, Advertiser responds with SCAN_RSP. • Timing : Scanner uses a scan interval (e.g., 50 ms) and scan window (e.g., 25 ms). Advertising and scanning must overlap for discovery. Connection Procedure  • Connection Process : o Host A (Central, Master): Initiates connection, takes GAP Central and GATT Client roles. o Host B (Peripheral, Slave): Advertises, accepts connection, takes GAP Peripheral and GATT Server roles. o States: Unassigned/Standby → Advertising (Host B) → Initiating (Host A) → Connecting → Connected. Bluetooth Security Concerns  • Security Concerns  o Authentication: Verifying device identity. o Integrity: Ensuring data is untampered. o Confidentiality: Preventing unauthorized data access. o Privacy: Protecting device tracking via Bluetooth address. • Security Risks : o Bluesnarfing: Stealing data (e.g., photos, messages) via Bluetooth. o Bluejacking: Sending unsolicited spam/phishing messages. o Bluebugging: Gaining backdoor access to spy or steal data. o Bluesmacking: DoS attack overwhelming device with large packets. o Car Whispering: Eavesdropping or injecting audio in Bluetooth-enabled car radios. • Statistic: A 2021 study showed 40–50% of IoT users leave Bluetooth on, increasing vulnerability.  Security in BLE  • Security Manager Features : o Pairing: Creating shared secret keys. o Bonding: Storing keys for future connections. o Authentication: Verifying shared keys. o Encryption: Using 128-bit AES for data security. o Message Integrity: Signing and verifying data beyond CRC checks. 
--- Page 12 ---
• Security Addressing : o Confidentiality via encryption. o Authentication via pairing/bonding. o Privacy via resolvable private addresses. o Integrity via digital signatures. • Security Levels : o Level 1: No security (no authentication/encryption). o Level 2: Unauthenticated pairing with encryption. o Level 3: Authenticated pairing with encryption. o Level 4: Authenticated LE Secure Connections with encryptio Lec 8   WiFi Bluetooth Zigbee Distance 20–150 meters 8–30 meters 20–150 meters Safety Safety is low, could be hacked easily Safety is good Safety is good and used for industry Power Consumption Power consumption is high Power consumption is low Power consumption is lowest Device Quantity Within 10 devices P2P, only several devices; some are only one device Mesh system; extendable to 150 devices Bandwidth Wide bandwidth; used for many data transmissions Narrow bandwidth Bandwidth is narrow but good for M2M communication Stability System is not stable when devices exceed 10 Reliability is low when devices exceed 10 High reliability; suitable for M2M communication Reliability Low, due to heavy use for data transmission; easily interfered Low when device number exceeds 10 High reliability; ideal for M2M use How WiFi Works : o Operates at 2.4 GHz in the unlicensed Industrial, Scientific, and Medical (ISM) frequency band, shared with protocols like ZigBee and Bluetooth. o Follows a standardized set of rules and specifications to ensure interoperability and performance. WiFi Definition and Standards : o WiFi stands for Wireless Fidelity. o Based on the IEEE 802.11 family of standards, designed for Local Area Networking (LAN). o Supports a peak physical-layer data rate of 54 Mbps with typical indoor coverage of 100 feet. Key Features of WiFi Technology : o Standard Compatibility: Ensures devices work across different vendors. o Extensive IoT Product Portfolio: Supports diverse connected devices. o Reliable, Sophisticated Connectivity: High-quality, stable connections. o Location Awareness: Supports location-based services. o Pervasive Connectivity: Ubiquitous network access. o Backward Compatibility: Works with older WiFi standards. o Proven WPA Security: Robust security protocols. o Cost-Effective, Simple Deployment: Easy to set up and use. 
--- Page 13 ---
WiFi Alliance : o A global, nonprofit industry association with over 600 companies. o Focuses on seamless interoperability, technology development, market building, and regulatory programs. o Certified over 3,600 new products last year.  WiFi Definition and Standards : • WiFi stands for Wireless Fidelity. • Based on the IEEE 802.11 family of standards, designed for Local Area Networking (LAN). • Supports a peak physical-layer data rate of 54 Mbps with typical indoor coverage of 100 feet. 
                                              Architecture of Infrastructure Network : • Station (STA): A device (e.g., laptop, phone) with wireless access mechanisms. • Basic Service Set (BSS): A group of STAs using the same frequency. • Access Point (AP): Integrates wireless LAN with the distribution system. • Portal: Bridges wireless to wired networks. • Distribution System: Interconnects multiple BSS to form an ESS. MIMO and Resource Units (RU)  • Multiple-Input Multiple-Output (MIMO o Uses multiple transmitters and receivers to transfer more data simultaneously. o Supported by all 802.11n devices, enabling higher speeds than non-802.11n products. • Resource Unit (RU)  o Introduced in WiFi 6 (802.11ax). o Divides the channel into smaller sub-channels (e.g., 26-tone RUs) for efficient data allocation to multiple devices. WiFi Security Protocols  • Encryption Standards : o WEP (Wired Equivalent Privacy): § First 802.11 security standard, easily hacked due to 24-bit IV and weak authentication. § Uses RC4 stream cipher with 64- or 128-bit keys. o WPA (Wi-Fi Protected Access): 

--- Page 14 ---
§ Interim standard to fix WEP flaws, backward compatible. § Uses RC4 with longer IVs and 256-bit keys with TKIP. § Modes: Personal (PSK) and Enterprise (802.1x and EAP). o WPA2: § Current standard, uses CCMP and AES for stronger encryption. § Supports Personal and Enterprise modes. Feature WEP WPA WPA2 WPA3 Description Wired-like privacy Based on 802.11i Full 802.11i features Wi-Fi Alliance standard Encryption RC4 TKIP + RC4 CCMP/AES GCMP-256 Authentication WEP-Open, WEP-Shared WPA-PSK, WPA-Enterprise WPA2-Personal, WPA2-Enterprise WPA3-Personal, WPA3-Enterprise Data Integrity CRC-32 - Cipher Block Chaining BIP-GMAC-256 Key Management None 4-way handshake 4-way handshake ECDH and ECDSA Lec 9 Media	Access	Control	(MAC)	• Mechanism	allows	multiple	devices	to	share	communication	medium	with	less	interference.	Multiplexing	• Technique	allows	multiple	users	to	share	communication	minimal	interference.	Frequency	Division	Multiplexing	(FDM)	• Divides	medium	frequency	band	to	smaller	channels,	each	assigned	to	user.	• Guard	Band:	Frequency	gap	between	channels	prevents	interference.	• Advantage:	Simple,	receiver	only	needs	to	tune	frequency,	send	-	used	in	radio.	• Disadvantage:	Inefficient	for	mobile	communication,	wastes	spectrum,	fixed	frequency.	• Example:	Five	channels,	each	100	kHz,	guard	band	10	kHz.	• (N-channel	x	N-band)	+	(N-channel	-	1)	x	Guard	• 5	x	100	+	(5	-	1)	x	10	=	540	kHz	Frequency	Division	Duplex	(FDD)	• Simultaneous	uplink	and	downlink.	• Needs	paired	spectrum	(UL/DL	ratio).	
--- Page 15 ---
Time	Division	Multiplexing	(TDM)	• Allocates	entire	bandwidth	to	each	user	for	specific	time	slot.	• Guardspace:	Time	gap	prevents	channel	interference.	Two	Schemes	1. Synchronous	(STDM):	Fixed	time	slot.	• Example	of	Synchronous:	• Input	Bit	Duration	=	1	/	Bitrate	• Output	Duration	=	Input	Duration	/	N	Inputs	• Output	Bit	Rate	=	N	x	Input	Bit	Rate	• Advantage:	Simple,	predictable.	• Disadvantage:	Wastes	slots.	2. Statistical	(Asynchronous):	Dynamic	allocation	(Circuit	Switch).	Hybrid	(TDM	&	FDM)	• Example:	GSM	(Global	Mobile	System).	• Advantage:	Protection	against	tapping,	resistance	to	frequency	interference.	• Disadvantage:	Requires	precise	coordination.	Code	Division	Multiplexing	(CDM)	• Uses	unique	codes	(chip	sequences)	to	separate	users,	not	time	or	frequency.	• CDMA	(Code	Division	Multiple	Access)	is	used	in	cellular	systems.	• Chip	sequences	are	orthogonal	vectors	to	avoid	interference.		Space	Division	Multiple	Access	(SDMA)	• Uses	physical	separation	distance	between	senders	to	avoid	interference.	

--- Page 16 ---
• Example:	FM	radio	stations	in	different	regions	use	the	same	frequency	without	interference	due	to	geographic	separation.	Lec		10		Cellular	System	Generations	• 2G:	14.4	kbps	• 3G:	500-700	kbps	• 3.5G:	1-3	mbps	• 4G:	5	mbps	Cellular	System	Overview	• Early	mobile	radio:	Single	high-power	transmitter,	no	frequency	reuse.	• Cellular	concept:	Multiple	low-power	transmitters	instead	of	a	single	one.	• Infrastructure:	With	variable	power,	adjusting	all	based	on	subscriber	demand	and	density.	• Frequency	reuse:	Channel	used	in	a	cell	can	be	reused	in	another	cell	at	sufficient	distance,	avoiding	interference.		System	Basic	Com ponents	1. MSC	(Mobile	Switching	Center)	:	Coordinate	Base	Station.	Activities	Connect	cellular	system	to	PSTN	and	handle	billing/maintenance	2. MSS	(Mobile	Station)	• BS	(Base	Station)	• MS	(Mobile	Station)	• PSIN	(Phone	Station)	Public	Switched	Telephone	Network	(PSTN)	Mobile	Station	(MS)	• Normal	mobile	phone	transceiver,	antenna	for	data/voice	transmission.	• GSM	Network,	includes	ME	(Mobile	Equipment)	and	SIM	card.	

--- Page 17 ---
Base	Station	(BS)	/	Base	Transceiver	Station	(BTS)	• Fixed	station	with	still	for	radio	communication,	have	transmit/receive	antenna.	Air	Interface	Standards	• Protocols	(FDMA,	TDMA,	CDMA)	allow	multiple	users	to	share	limited	radio	channel	simultaneously.	Database	(MSC)	• Home	Location	Register	(HLR):	Main	subscriber	city	info.	• Visitor	Location	Register	(VLR):	Temp	subscriber	city	info	(roaming).	Security	Mechanism	• Authenticate	subscriber	and	prevent	unauthorized	access	or	billing	fraud.	• Database:	Equipment	Identity	Register	(EIR)	to	identify	stolen	phones.	• Authentication	Center	(AuC):	Handle	encryption/decryption.	Gateway	• Link	wireless	or	wireline-wired	systems	comprising	MSC,	IWF.	• MSC	connects	to	PSTN	or	other	MSCs.	• IWF	(Interworking	Function)	bridge	2G,	3G,	4G,	WiFi	perform	protocol	translation.	Frequency	Reuse	and	Cluster	Concepts	• Frequency:	Reusing	frequency	after	distance	to	avoid	interference	(reuse	factor).	• Cluster:	Group	of	base	stations	controlled	by	one	MSC	denoted	by	(N).	Advantages	and	Disadvantages	of	Cellular	System	• Pros:	Higher	capacity,	less	transmission	power,	local	interference	only.	• Cons:	Expensive	infrastructure,	handover	needed,	frequency	planning	Lec11	Basic	Operation	of	Transmitting	Calls	• Stages:	a)	Monitor	for	strongest	signal.	b)	Request	for	connection.	c)	Paging.	d)	Call	accepted.	e)	Ongoing	call.	f)	Handoff.	Steps	for	Transmitting	Calls	1. Caller	enters	10-digit	phone	number	and	sends.	2. MS	scans	for	a	free	channel	and	sends	the	number.	
--- Page 18 ---
3. BS	relays	the	number	to	MSC.	4. MSC	dispatches	the	request	to	all	BSs.	5. MIN	(Mobile	Identification	Number)	paging.	6. MS	responds	via	reverse	control	channel.	7. BS	relays	ACK	to	MSC.	8. MSC	assigns	an	unused	voice	channel.	Steps	for	Receiving	Calls	1. Idle	MSC	continuously	listens	to	paging.	2. Pager	sent	to	caller,	MSC	to	BS.	3. Pager	sent	to	BS	in	MS's	current	cell.	4. MS	responds	on	control	channel.	5. Voice	channel	assigned	and	ringing	starts	at	MS.	Mobility	Management	• Location	Area:	Home	networks	assigned	to	an	MS.	• Handoff	Management:	Procedure	to	maintain	service	continuity	when	MS	moves	between	BSs.	• Home	Agent:	Tracks	current	location	of	MS.	• Location	Management:	Tracks	MS's	location	between	calls.	Handoff	Process	1. BS	notices	MS	signal	fading.	2. Neighbor	BSs	report	signal	strength.	3. BS	transfers	ownership	to	BS	with	the	strongest	signal.	4. MSC	changes	channel	carrying	the	call.	Handoff	Stages	and	Types	• Initiation:	MS	needs	a	handoff.	• Resource	Reservation:	Allocate	voice	and	control	channels.	• Execution:	Transfer	control	to	other	BS.	• Completion:	Releases	unnecessary	resources.	Types	of	Handoff	• Hard	Handoff:	Old	BS	connection	breaks	before	new	connection.	• Soft	Handoff:	MS	connects	with	old/new	BSs	during	transition.	
--- Page 19 ---
Location	Management	• Tracks	active	(power-on)	MS	within	the	cellular	network.	• Operating	by	paging,	location	updates,	MSs.	Location	Update	Algorithm	• Static:	Triggered	by	crossing	cell	boundaries.	• Dynamic:	Time-based,	movement-based,	distance-based,	N	cell	crossing	 