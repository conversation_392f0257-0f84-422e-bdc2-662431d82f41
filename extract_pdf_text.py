#!/usr/bin/env python3
import PyPDF2
import sys
import os

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file using PyPDF2"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            print(f"PDF has {len(pdf_reader.pages)} pages")
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                text += f"\n--- Page {page_num + 1} ---\n"
                text += page_text
                
            return text
    except Exception as e:
        print(f"Error extracting text: {e}")
        return None

if __name__ == "__main__":
    pdf_path = "/Users/<USER>/Desktop/dummy/Iot -- تفريغ .pdf"
    
    if not os.path.exists(pdf_path):
        print(f"PDF file not found: {pdf_path}")
        sys.exit(1)
    
    extracted_text = extract_text_from_pdf(pdf_path)
    
    if extracted_text:
        # Save extracted text to a file
        output_file = "extracted_pdf_content.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(extracted_text)
        print(f"Text extracted and saved to {output_file}")
        
        # Also print first 2000 characters to see what we got
        print("\n--- First 2000 characters of extracted text ---")
        print(extracted_text[:2000])
    else:
        print("Failed to extract text from PDF")
